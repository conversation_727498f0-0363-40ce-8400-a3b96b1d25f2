#!/usr/bin/env python3
"""
Transformer implementation for machine translation
"""
import tensorflow as tf
import numpy as np


def positional_encoding(max_seq_len, dm):
    """
    Calculate the positional encoding for a transformer

    Args:
        max_seq_len (int): Maximum sequence length
        dm (int): Model depth

    Returns:
        numpy.ndarray: Positional encoding vectors of shape (max_seq_len, dm)
    """
    # Initialize the positional encoding matrix
    PE = np.zeros((max_seq_len, dm))

    # Create position indices (0, 1, 2, ..., max_seq_len-1)
    position = np.arange(max_seq_len)[:, np.newaxis]  # Shape: (max_seq_len, 1)

    # Create dimension indices (0, 1, 2, ..., dm-1)
    # We need to calculate for every pair of dimensions (2i, 2i+1)
    div_term = np.exp(np.arange(0, dm, 2) * -(np.log(10000.0) / dm))

    # Apply sine to even indices
    PE[:, 0::2] = np.sin(position * div_term)

    # Apply cosine to odd indices
    PE[:, 1::2] = np.cos(position * div_term)

    return PE


def sdp_attention(Q, K, V, mask=None):
    """
    Calculate the scaled dot product attention

    Args:
        Q: tensor with last two dimensions (..., seq_len_q, dk)
            containing query matrix
        K: tensor with last two dimensions (..., seq_len_v, dk)
            containing key matrix
        V: tensor with last two dimensions (..., seq_len_v, dv)
            containing value matrix
        mask: tensor that can be broadcast into (..., seq_len_q, seq_len_v)
            containing optional mask

    Returns:
        output: tensor with last two dimensions (..., seq_len_q, dv)
            containing scaled dot product attention
        weights: tensor with last two dimensions (..., seq_len_q, seq_len_v)
            containing attention weights
    """
    # Get the dimension of the key vectors (dk)
    dk = tf.cast(tf.shape(K)[-1], tf.float32)

    # Calculate Q * K^T
    # Q shape: (..., seq_len_q, dk)
    # K shape: (..., seq_len_v, dk)
    # matmul result shape: (..., seq_len_q, seq_len_v)
    matmul_qk = tf.matmul(Q, K, transpose_b=True)

    # Scale by sqrt(dk)
    scaled_attention_logits = matmul_qk / tf.math.sqrt(dk)

    # Apply mask if provided
    if mask is not None:
        # Add large negative value to masked positions
        scaled_attention_logits += (mask * -1e9)

    # Apply softmax to get attention weights
    # Shape: (..., seq_len_q, seq_len_v)
    attention_weights = tf.nn.softmax(scaled_attention_logits, axis=-1)

    # Apply attention weights to values
    # attention_weights shape: (..., seq_len_q, seq_len_v)
    # V shape: (..., seq_len_v, dv)
    # output shape: (..., seq_len_q, dv)
    output = tf.matmul(attention_weights, V)

    return output, attention_weights


class MultiHeadAttention(tf.keras.layers.Layer):
    """MultiHeadAttention class to perform multi-head attention."""

    def __init__(self, dm, h):
        """Initialize the MultiHeadAttention layer.

        Args:
            dm (int): The dimensionality of the model.
            h (int): The number of attention heads.
        """
        super(MultiHeadAttention, self).__init__()

        if dm % h != 0:
            raise ValueError("dm must be divisible by h")

        self.h = h
        self.dm = dm
        self.depth = dm // h

        # Layers to generate Query, Key, and Value matrices
        self.Wq = tf.keras.layers.Dense(dm)
        self.Wk = tf.keras.layers.Dense(dm)
        self.Wv = tf.keras.layers.Dense(dm)
        # Linear layer to combine the outputs of all attention heads
        self.linear = tf.keras.layers.Dense(dm)

    def split_heads(self, x, batch_size):
        """Split the last dimension into (h, depth) and transpose.

        Args:
            x (tensor): Input tensor to be split.
            batch_size (int): The batch size.

        Returns:
            Tensor: Transposed tensor of shape (batch_size, h, seq_len, depth)
        """
        x = tf.reshape(x, (batch_size, -1, self.h, self.depth))
        return tf.transpose(x, perm=[0, 2, 1, 3])

    def call(self, Q, K, V, mask):
        """Perform multi-head attention.

        Args:
            Q (tensor): Query matrix.
            K (tensor): Key matrix.
            V (tensor): Value matrix.
            mask (tensor or None): Mask to apply.

        Returns:
            output (tensor): Multi-head attention output.
            weights (tensor): Attention weights.
        """
        batch_size = tf.shape(Q)[0]

        # Generate Q, K, V matrices
        Q = self.Wq(Q)
        K = self.Wk(K)
        V = self.Wv(V)

        # Split Q, K, V into multiple heads
        Q = self.split_heads(Q, batch_size)
        K = self.split_heads(K, batch_size)
        V = self.split_heads(V, batch_size)

        # Apply scaled dot-product attention
        scaled_attention, weights = sdp_attention(Q, K, V, mask)

        # Concatenate the attention output for all heads
        scaled_attention = tf.transpose(scaled_attention, perm=[0, 2, 1, 3])
        concat_attention = tf.reshape(scaled_attention,
                                      (batch_size, -1, self.dm))

        # Apply the final linear layer
        output = self.linear(concat_attention)

        return output, weights


class EncoderBlock(tf.keras.layers.Layer):
    """Encoder block for transformer"""

    def __init__(self, dm, h, hidden, drop_rate=0.1):
        """
        Initialize the encoder block

        Args:
            dm (int): Dimensionality of the model
            h (int): Number of heads
            hidden (int): Number of hidden units in the fully connected layer
            drop_rate (float): Dropout rate
        """
        super(EncoderBlock, self).__init__()

        self.mha = MultiHeadAttention(dm, h)
        self.ffn = tf.keras.Sequential([
            tf.keras.layers.Dense(hidden, activation='relu'),
            tf.keras.layers.Dense(dm)
        ])

        self.layernorm1 = tf.keras.layers.LayerNormalization(epsilon=1e-6)
        self.layernorm2 = tf.keras.layers.LayerNormalization(epsilon=1e-6)

        self.dropout1 = tf.keras.layers.Dropout(drop_rate)
        self.dropout2 = tf.keras.layers.Dropout(drop_rate)

    def call(self, x, training, mask):
        """
        Forward pass through encoder block

        Args:
            x: Input tensor
            training: Boolean indicating training mode
            mask: Mask to apply

        Returns:
            Output tensor
        """
        attn_output, _ = self.mha(x, x, x, mask)
        attn_output = self.dropout1(attn_output, training=training)
        out1 = self.layernorm1(x + attn_output)

        ffn_output = self.ffn(out1)
        ffn_output = self.dropout2(ffn_output, training=training)
        out2 = self.layernorm2(out1 + ffn_output)

        return out2


class DecoderBlock(tf.keras.layers.Layer):
    """Decoder block for transformer"""

    def __init__(self, dm, h, hidden, drop_rate=0.1):
        """
        Initialize the decoder block

        Args:
            dm (int): Dimensionality of the model
            h (int): Number of heads
            hidden (int): Number of hidden units in the fully connected layer
            drop_rate (float): Dropout rate
        """
        super(DecoderBlock, self).__init__()

        self.mha1 = MultiHeadAttention(dm, h)
        self.mha2 = MultiHeadAttention(dm, h)

        self.ffn = tf.keras.Sequential([
            tf.keras.layers.Dense(hidden, activation='relu'),
            tf.keras.layers.Dense(dm)
        ])

        self.layernorm1 = tf.keras.layers.LayerNormalization(epsilon=1e-6)
        self.layernorm2 = tf.keras.layers.LayerNormalization(epsilon=1e-6)
        self.layernorm3 = tf.keras.layers.LayerNormalization(epsilon=1e-6)

        self.dropout1 = tf.keras.layers.Dropout(drop_rate)
        self.dropout2 = tf.keras.layers.Dropout(drop_rate)
        self.dropout3 = tf.keras.layers.Dropout(drop_rate)

    def call(self, x, enc_output, training, look_ahead_mask, padding_mask):
        """
        Forward pass through decoder block

        Args:
            x: Input tensor
            enc_output: Encoder output
            training: Boolean indicating training mode
            look_ahead_mask: Look ahead mask
            padding_mask: Padding mask

        Returns:
            Output tensor and attention weights
        """
        attn1, attn_weights_block1 = self.mha1(x, x, x, look_ahead_mask)
        attn1 = self.dropout1(attn1, training=training)
        out1 = self.layernorm1(attn1 + x)

        attn2, attn_weights_block2 = self.mha2(
            out1, enc_output, enc_output, padding_mask)
        attn2 = self.dropout2(attn2, training=training)
        out2 = self.layernorm2(attn2 + out1)

        ffn_output = self.ffn(out2)
        ffn_output = self.dropout3(ffn_output, training=training)
        out3 = self.layernorm3(ffn_output + out2)

        return out3, attn_weights_block1, attn_weights_block2


class Encoder(tf.keras.layers.Layer):
    """Encoder for transformer"""

    def __init__(self, N, dm, h, hidden, input_vocab, max_seq_len, drop_rate=0.1):
        """
        Initialize the encoder

        Args:
            N (int): Number of blocks in the encoder
            dm (int): Dimensionality of the model
            h (int): Number of heads
            hidden (int): Number of hidden units in the fully connected layer
            input_vocab (int): Size of the input vocabulary
            max_seq_len (int): Maximum sequence length possible
            drop_rate (float): Dropout rate
        """
        super(Encoder, self).__init__()

        self.dm = dm
        self.N = N

        self.embedding = tf.keras.layers.Embedding(input_vocab, dm)
        self.positional_encoding = positional_encoding(max_seq_len, self.dm)

        self.enc_layers = [EncoderBlock(dm, h, hidden, drop_rate)
                           for _ in range(N)]

        self.dropout = tf.keras.layers.Dropout(drop_rate)

    def call(self, x, training, mask):
        """
        Forward pass through encoder

        Args:
            x: Input tensor
            training: Boolean indicating training mode
            mask: Mask to apply

        Returns:
            Output tensor
        """
        seq_len = tf.shape(x)[1]

        # Adding embedding and position encoding
        x = self.embedding(x)  # (batch_size, input_seq_len, dm)
        x *= tf.math.sqrt(tf.cast(self.dm, tf.float32))

        # Get positional encoding for the sequence length
        pos_encoding = tf.cast(self.positional_encoding, tf.float32)
        pos_encoding = pos_encoding[:seq_len, :]
        x += pos_encoding

        x = self.dropout(x, training=training)

        for i in range(self.N):
            x = self.enc_layers[i](x, training=training, mask=mask)

        return x  # (batch_size, input_seq_len, dm)


class Decoder(tf.keras.layers.Layer):
    """Decoder for transformer"""

    def __init__(self, N, dm, h, hidden, target_vocab, max_seq_len, drop_rate=0.1):
        """
        Initialize the decoder

        Args:
            N (int): Number of blocks in the decoder
            dm (int): Dimensionality of the model
            h (int): Number of heads
            hidden (int): Number of hidden units in the fully connected layer
            target_vocab (int): Size of the target vocabulary
            max_seq_len (int): Maximum sequence length possible
            drop_rate (float): Dropout rate
        """
        super(Decoder, self).__init__()

        self.dm = dm
        self.N = N

        self.embedding = tf.keras.layers.Embedding(target_vocab, dm)
        self.positional_encoding = positional_encoding(max_seq_len, self.dm)

        self.dec_layers = [DecoderBlock(dm, h, hidden, drop_rate)
                           for _ in range(N)]
        self.dropout = tf.keras.layers.Dropout(drop_rate)

    def call(self, x, enc_output, training, look_ahead_mask, padding_mask):
        """
        Forward pass through decoder

        Args:
            x: Input tensor
            enc_output: Encoder output
            training: Boolean indicating training mode
            look_ahead_mask: Look ahead mask
            padding_mask: Padding mask

        Returns:
            Output tensor and attention weights
        """
        seq_len = tf.shape(x)[1]
        attention_weights = {}

        x = self.embedding(x)  # (batch_size, target_seq_len, dm)
        x *= tf.math.sqrt(tf.cast(self.dm, tf.float32))

        # Get positional encoding for the sequence length
        pos_encoding = tf.cast(self.positional_encoding, tf.float32)
        pos_encoding = pos_encoding[:seq_len, :]
        x += pos_encoding

        x = self.dropout(x, training=training)

        for i in range(self.N):
            x, block1, block2 = self.dec_layers[i](x, enc_output, training=training,
                                                    look_ahead_mask=look_ahead_mask,
                                                    padding_mask=padding_mask)

            attention_weights[f'decoder_layer{i+1}_block1'] = block1
            attention_weights[f'decoder_layer{i+1}_block2'] = block2

        # x.shape == (batch_size, target_seq_len, dm)
        return x, attention_weights


class Transformer(tf.keras.Model):
    """Transformer model for machine translation"""

    def __init__(self, N, dm, h, hidden, input_vocab, target_vocab,
                 max_seq_input, max_seq_target, drop_rate=0.1):
        """
        Initialize the transformer

        Args:
            N (int): Number of blocks in the encoder and decoder
            dm (int): Dimensionality of the model
            h (int): Number of heads
            hidden (int): Number of hidden units in the fully connected layers
            input_vocab (int): Size of the input vocabulary
            target_vocab (int): Size of the target vocabulary
            max_seq_input (int): Maximum sequence length possible for the input
            max_seq_target (int): Maximum sequence length possible for the target
            drop_rate (float): Dropout rate
        """
        super(Transformer, self).__init__()

        self.encoder = Encoder(N, dm, h, hidden, input_vocab,
                               max_seq_input, drop_rate)

        self.decoder = Decoder(N, dm, h, hidden, target_vocab,
                               max_seq_target, drop_rate)

        self.final_layer = tf.keras.layers.Dense(target_vocab)

    def call(self, inputs, target, training, enc_padding_mask,
             look_ahead_mask, dec_padding_mask):
        """
        Forward pass through transformer

        Args:
            inputs: Input tensor
            target: Target tensor
            training: Boolean indicating training mode
            enc_padding_mask: Encoder padding mask
            look_ahead_mask: Look ahead mask
            dec_padding_mask: Decoder padding mask

        Returns:
            Output tensor and attention weights
        """
        # (batch_size, inp_seq_len, dm)
        enc_output = self.encoder(inputs, training=training, mask=enc_padding_mask)

        # dec_output.shape == (batch_size, tar_seq_len, dm)
        dec_output, attention_weights = self.decoder(
            target, enc_output, training=training,
            look_ahead_mask=look_ahead_mask, padding_mask=dec_padding_mask)

        # (batch_size, tar_seq_len, target_vocab_size)
        final_output = self.final_layer(dec_output)

        return final_output, attention_weights
