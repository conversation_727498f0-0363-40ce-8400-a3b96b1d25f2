#!/usr/bin/env python3
"""
Train transformer model for machine translation
"""
import tensorflow as tf

Dataset = __import__('3-dataset').Dataset
create_masks = __import__('4-create_masks').create_masks
Transformer = __import__('5-transformer').Transformer


class CustomSchedule(tf.keras.optimizers.schedules.LearningRateSchedule):
    """Custom learning rate schedule for transformer training"""

    def __init__(self, dm, warmup_steps=4000):
        """
        Initialize the learning rate schedule

        Args:
            dm (int): Dimensionality of the model
            warmup_steps (int): Number of warmup steps
        """
        super(CustomSchedule, self).__init__()

        self.dm = dm
        self.dm = tf.cast(self.dm, tf.float32)
        self.warmup_steps = warmup_steps

    def __call__(self, step):
        """
        Calculate learning rate for given step

        Args:
            step: Current training step

        Returns:
            Learning rate
        """
        step = tf.cast(step, tf.float32)
        arg1 = tf.math.rsqrt(step)
        arg2 = step * (self.warmup_steps ** -1.5)

        return tf.math.rsqrt(self.dm) * tf.math.minimum(arg1, arg2)


def loss_function(real, pred):
    """
    Calculate sparse categorical crossentropy loss, ignoring padded tokens

    Args:
        real: Real target values
        pred: Predicted values

    Returns:
        Loss value
    """
    mask = tf.math.logical_not(tf.math.equal(real, 0))
    loss_ = tf.keras.losses.sparse_categorical_crossentropy(
        real, pred, from_logits=True)

    mask = tf.cast(mask, dtype=loss_.dtype)
    loss_ *= mask

    return tf.reduce_sum(loss_) / tf.reduce_sum(mask)


def accuracy_function(real, pred):
    """
    Calculate accuracy, ignoring padded tokens

    Args:
        real: Real target values
        pred: Predicted values

    Returns:
        Accuracy value
    """
    accuracies = tf.equal(real, tf.argmax(pred, axis=2))

    mask = tf.math.logical_not(tf.math.equal(real, 0))
    accuracies = tf.math.logical_and(mask, accuracies)

    accuracies = tf.cast(accuracies, dtype=tf.float32)
    mask = tf.cast(mask, dtype=tf.float32)
    return tf.reduce_sum(accuracies) / tf.reduce_sum(mask)


@tf.function
def train_step(inp, tar, transformer, optimizer, train_loss, train_accuracy):
    """
    Single training step

    Args:
        inp: Input tensor
        tar: Target tensor
        transformer: Transformer model
        optimizer: Optimizer
        train_loss: Training loss metric
        train_accuracy: Training accuracy metric

    Returns:
        None
    """
    tar_inp = tar[:, :-1]
    tar_real = tar[:, 1:]

    enc_padding_mask, combined_mask, dec_padding_mask = create_masks(inp, tar_inp)

    with tf.GradientTape() as tape:
        predictions, _ = transformer(inp, tar_inp,
                                     training=True,
                                     enc_padding_mask=enc_padding_mask,
                                     look_ahead_mask=combined_mask,
                                     dec_padding_mask=dec_padding_mask)
        loss = loss_function(tar_real, predictions)

    gradients = tape.gradient(loss, transformer.trainable_variables)
    optimizer.apply_gradients(zip(gradients, transformer.trainable_variables))

    train_loss(loss)
    train_accuracy(accuracy_function(tar_real, predictions))


def train_transformer(N, dm, h, hidden, max_len, batch_size, epochs):
    """
    Creates and trains a transformer model for machine translation

    Args:
        N (int): Number of blocks in the encoder and decoder
        dm (int): Dimensionality of the model
        h (int): Number of heads
        hidden (int): Number of hidden units in the fully connected layers
        max_len (int): Maximum number of tokens per sequence
        batch_size (int): Batch size for training
        epochs (int): Number of epochs to train for

    Returns:
        Trained transformer model
    """
    # Create dataset
    data = Dataset(batch_size, max_len)

    # Get vocabulary sizes
    input_vocab_size = data.tokenizer_pt.vocab_size + 2
    target_vocab_size = data.tokenizer_en.vocab_size + 2

    # Create transformer model
    transformer = Transformer(N, dm, h, hidden, input_vocab_size,
                              target_vocab_size, max_len, max_len)

    # Create learning rate schedule and optimizer
    learning_rate = CustomSchedule(dm)
    optimizer = tf.keras.optimizers.Adam(learning_rate, beta_1=0.9,
                                         beta_2=0.98, epsilon=1e-9)

    # Create loss and accuracy metrics
    train_loss = tf.keras.metrics.Mean(name='train_loss')
    train_accuracy = tf.keras.metrics.Mean(name='train_accuracy')

    # Training loop
    for epoch in range(epochs):
        train_loss.reset_state()
        train_accuracy.reset_state()

        batch_count = 0
        for (batch, (inp, tar)) in enumerate(data.data_train):
            train_step(inp, tar, transformer, optimizer, train_loss, train_accuracy)

            if batch % 50 == 0:
                print(f'Epoch {epoch + 1}, Batch {batch}: Loss {train_loss.result()}, Accuracy {train_accuracy.result()}')

            batch_count = batch

        print(f'Epoch {epoch + 1}: Loss {train_loss.result()}, Accuracy {train_accuracy.result()}')

    return transformer
